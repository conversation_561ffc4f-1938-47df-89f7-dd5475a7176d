import store from '../../utils/store.js'
import handleError from '../../utils/handleError.js'
import { setToken, clearToken } from '../../utils/token.js'
import makeClient from '../../utils/makeClient.js'

const client = makeClient()

Page({
  data: {
    logoUrl: '',
    domainInfo: null,
    countdown: 0,
    phone: '',
    password: '',
    newPassword: '',
    confirmPassword: '',
    code: '',
    captcha: {
      url: '',
      answer: '',
      token: ''
    },
    otpToken: '',
    loginType: 'smsCode'
  },

  async onLoad() {
    try {
      clearToken()
      var domainInfo = this.getDomainInfo()

      const [err, r] = await client.domainInfo({
        body: {
          domain: domainInfo.domainName
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.setData({ domainInfo: r })
      store.setItem('domainInfo', r)
      domainInfo = r

      if (domainInfo && domainInfo.logoUrl) {
        const logoUrl = this.formatImageURL(domainInfo.logoUrl)
        this.setData({ logoUrl })
      }

      await this.refreshCaptcha()
    } catch (e) {
      console.error(e)
    }
  },
  
  onUnload() {
    // 页面卸载时清除计时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
  },
  async refreshCaptcha() {
    const domainInfo = this.getDomainInfo()

    const [err, r] = await client.createCaptcha()
    if (err) {
      handleError(err)
      return
    }

    const baseUrl = `https://${domainInfo.domainName}`
    const captchaUrl = `${baseUrl}/api/public/captcha?token=${encodeURIComponent(
      r
    )}`

    this.setData({
      'captcha.url': captchaUrl,
      'captcha.token': r,
      'captcha.answer': '' // 清空验证码输入框
    })
  },
  onCaptchaInput(e) {
    this.setData({
      'captcha.answer': e.detail.value
    })
  },

  async getVerificationCode() {
    if(!this.data.phone) {
      handleError('请输入手机号')
      return
    }
    if (this.data.phone.length !== 11) {
      handleError('请输入正确的手机号')
      return
    }
    if(!this.data.captcha.answer) {
      handleError('请输入图形验证码')
      return
    }
    if (this.data.countdown > 0) {
      return
    }
    const [err, r] = await client.apiSendLoginSms({
      body: {
        receiver: this.data.phone,
        captchaToken: this.data.captcha.token,
        captchaAnswer: this.data.captcha.answer
      }
    })

    if (err) {
      handleError(err)
      this.refreshCaptcha()
      return
    }
    // 清除可能存在的旧计时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
    
    this.setData({
      countdown: 60,
      otpToken: r.token
    })

    this.countdownTimer = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({
          countdown: this.data.countdown - 1
        })
      } else {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }
    }, 1000)
  },

  async getForgetPasswordCode() {
    if(!this.data.phone) {
      handleError('请输入手机号')
      return
    }
    if (this.data.phone.length !== 11) {
      handleError('请输入正确的手机号')
      return
    }
    if(!this.data.captcha.answer) {
      handleError('请输入图形验证码')
      return
    }
    if(this.data.captcha.answer.length !== 4) {
      handleError('请输入正确的图形验证码')
      return
    }
    if (this.data.countdown > 0) {
      return
    }
    const [err, r] = await client.apiSendResetPasswordSms({
      body: {
        receiver: this.data.phone,
        captchaToken: this.data.captcha.token,
        captchaAnswer: this.data.captcha.answer
      }
    })

    if (err) {
      handleError(err)
      this.refreshCaptcha()
      return
    }
    // 清除可能存在的旧计时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
    
    this.setData({
      countdown: 60,
      otpToken: r.token
    })

    this.countdownTimer = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({
          countdown: this.data.countdown - 1
        })
      } else {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }
    }, 1000)
  },

  getDomainInfo() {
    var domainInfo = store.getItem('domainInfo')
    if (!domainInfo) {
      domainInfo = {
        domainName: '156-dev.olading.com'
      }
    }
    return domainInfo
  },
  // 绑定输入框
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value })
  },
  onPasswordInput(e) {
    this.setData({ password: e.detail.value })
  },
  onCodeInput(e) {
    this.setData({ code: e.detail.value })
  },
  onNewPasswordInput(e) {
    this.setData({ newPassword: e.detail.value })
  },
  onConfirmPasswordInput(e) {
    this.setData({ confirmPassword: e.detail.value })
  },
  formatImageURL(id) {
    var domainInfo = store.getItem('domainInfo')
    const baseUrl = `https://${domainInfo.domainName}`

    return `${baseUrl}/api/public/previewFile/${id}`
  },

  handleLoginTypeClick(e) {
    this.setData({
      loginType: e.currentTarget.dataset.type,
      phone: '',
      password: '', // 清空密码输入
      captcha: { answer: ''},
      countdown: 0, // 重置倒计时
      code: '' // 清空验证码输入
    })
    this.refreshCaptcha()
  },
  handleForgetPassword() {
    this.setData({
      loginType: 'forgetPassword',
      phone: '',
      password: '', // 清空密码输入
      newPassword: '', // 清空新密码输入
      confirmPassword: '', // 清空确认密码输入
      captcha: { answer: ''},
      code: '',
      countdown: 0 // 重置倒计时
    })
    this.refreshCaptcha()
  },

  async login() {
    const { phone, code, captcha, otpToken, password } = this.data
    // 手机号验证
    if (!phone) {
      handleError('请输入手机号')
      return
    }
    if (phone.length !== 11) {
      handleError('请输入正确的手机号')
      return
    }
    
    // 图形验证码验证
    if (!captcha.answer) {
      handleError('请输入验证码')
      return
    }
    if (captcha.answer.length !== 4) {
      handleError('请输入正确的图形验证码')
      return
    }
    if (this.data.loginType === 'smsCode') {
      if (code.length !== 6) {
        handleError('请输入正确的验证码')
        return
      }
      if (!otpToken) {
        handleError('请先获取短信验证码')
        return
      }
    } else if (this.data.loginType === 'password') {
      if (!password) {
        handleError('请输入密码')
        return
      }
    }

    wx.showLoading({ title: '登录中...' })
    const [err, r] = await client.login({
      body: {
        account: phone,
        captchaAnswer: captcha.answer,
        captchaToken: captcha.token,
        otpToken: this.data.loginType === 'smsCode' ? otpToken : '',
        code: this.data.loginType === 'smsCode' ? code : '',
        smsLogin: this.data.loginType === 'smsCode' ? true : false,
        password: this.data.loginType === 'password' ? password : '',
        type: 'PERSONAL'
      }
    })

    wx.hideLoading()
    if (err) {
      handleError(err)
      this.refreshCaptcha()
      return
    }

    setToken(r.token)
    wx.reLaunch({ url: '/pages/index/index' })
  },

  async handleConfirm() {
    const { phone, code, otpToken, newPassword, confirmPassword } = this.data
    if (!phone) {
      handleError('请输入手机号')
      return
    }
    if (phone.length !== 11) {
      handleError('请输入正确的手机号')
      return
    }
    if (!code) {
      handleError('请输入短信验证码')
      return
    }
    if (!otpToken) {
      handleError('请先获取短信验证码')
      return
    }
    if (!newPassword) {
      handleError('请输入新密码')
      return
    }
    if (!confirmPassword) {
      handleError('请再次输入新密码')
      return
    }
    if (newPassword !== confirmPassword) {
      handleError('两次输入密码不一致')
      return
    }
    if (newPassword.length < 6 || newPassword.length > 20) {
      handleError('密码长度应为6-20位')
      return
    }

    const [err, r] = await client.apiResetPassword({
      body: {
        cellphone: phone,
        code: code,
        otpToken: otpToken,
        password: newPassword,
        confirmPassword: confirmPassword
      }
    })
    if (err) {
      handleError(err)
      return
    }
    wx.showToast({
      title: '密码修改成功',
      icon: 'success',
      duration: 1000
    })
    setTimeout(() => {
      wx.reLaunch({ url: '/pages/login/login' })
    }, 1000)
  }
})
